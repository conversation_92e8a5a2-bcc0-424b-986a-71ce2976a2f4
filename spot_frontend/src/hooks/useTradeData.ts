import { useState, useEffect, useCallback, useRef } from 'react';
import { websocketService } from '@/services/websocketService';

interface TradeDataUpdate {
  poolAddress: string;
  tradeData: any;
  tradeHistory: any[];
  timestamp: number;
}

interface FormattedTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: string;
  usdAmount: string;
  price: string;
  mc: string;
  trader: string;
  age: string;
  displayAmount: string;
  displayPrice: string;
  displayValueUsd: string;
  timeAgo: string;
  txHash: string;
  wallet: string;
}

interface UseTradeDataReturn {
  trades: FormattedTrade[];
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  lastUpdate: number | null;
  refetch: () => Promise<void>;
}

export const useTradeData = (poolAddress: string | null): UseTradeDataReturn => {
  const [trades, setTrades] = useState<FormattedTrade[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);
  
  const currentPoolAddress = useRef<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Format trade data for display
  const formatTrade = useCallback((trade: any): FormattedTrade => {
    const formatNumber = (num: number): string => {
      if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
      if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
      if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
      return num.toFixed(2);
    };

    const formatPrice = (price: number): string => {
      if (price < 0.001) return price.toExponential(3);
      if (price < 1) return price.toFixed(6);
      return price.toFixed(4);
    };

    const formatCurrency = (amount: number): string => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    };

    const getTimeAgo = (timestamp: number): string => {
      const now = Date.now();
      const diff = now - (timestamp * 1000);
      const seconds = Math.floor(diff / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);

      if (days > 0) return `${days}d`;
      if (hours > 0) return `${hours}h`;
      if (minutes > 0) return `${minutes}m`;
      return `${seconds}s`;
    };

    const getTraderName = (wallet: string): string => {
      if (!wallet) return 'Unknown';
      return wallet.slice(0, 3) + '...' + wallet.slice(-3);
    };

    return {
      id: trade.id || `${trade.timestamp}-${Math.random()}`,
      timestamp: trade.timestamp,
      type: trade.type,
      amount: formatNumber(trade.tokenAmount || trade.amount || 0),
      usdAmount: formatCurrency(trade.tokenAmountUsd || trade.value_usd || 0),
      price: formatPrice(trade.price || 0),
      mc: formatCurrency((trade.price || 0) * 1000000), // Rough market cap estimate
      trader: getTraderName(trade.wallet),
      age: getTimeAgo(trade.timestamp),
      displayAmount: trade.displayAmount || formatNumber(trade.tokenAmount || trade.amount || 0),
      displayPrice: trade.displayPrice || formatPrice(trade.price || 0),
      displayValueUsd: trade.displayValueUsd || formatCurrency(trade.tokenAmountUsd || trade.value_usd || 0),
      timeAgo: trade.timeAgo || getTimeAgo(trade.timestamp),
      txHash: trade.txHash || trade.tx_hash || '',
      wallet: trade.wallet || ''
    };
  }, []);

  // Fetch initial trade data from API
  const fetchInitialTradeData = useCallback(async (poolAddr: string) => {
    if (!poolAddr) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log(`📊 Fetching initial trade data for pool: ${poolAddr}`);
      
      const response = await fetch(`/api/trade/initial/${poolAddr}?limit=20`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch trade data: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.data.trades) {
        const formattedTrades = data.data.trades.map(formatTrade);
        setTrades(formattedTrades);
        setLastUpdate(Date.now());
        console.log(`✅ Loaded ${formattedTrades.length} initial trades`);
      } else {
        console.warn('No trade data received from API');
        setTrades([]);
      }
    } catch (err: any) {
      console.error('❌ Failed to fetch initial trade data:', err);
      setError(err.message);
      setTrades([]);
    } finally {
      setIsLoading(false);
    }
  }, [formatTrade]);

  // Handle real-time trade data updates
  const handleTradeDataUpdate = useCallback((update: TradeDataUpdate) => {
    if (update.poolAddress !== currentPoolAddress.current) {
      return; // Ignore updates for different pools
    }

    console.log(`📊 Processing trade update for pool: ${update.poolAddress}`);
    
    if (update.tradeHistory && update.tradeHistory.length > 0) {
      const formattedTrades = update.tradeHistory.map(formatTrade);
      setTrades(formattedTrades);
      setLastUpdate(update.timestamp);
      setError(null);
    }
  }, [formatTrade]);

  // Subscribe to WebSocket trade data
  const subscribeToTradeData = useCallback(async (poolAddr: string) => {
    try {
      console.log(`📡 Subscribing to trade WebSocket for pool: ${poolAddr}`);
      
      // Subscribe to trade data updates
      const unsubscribe = websocketService.onTradeData(handleTradeDataUpdate);
      unsubscribeRef.current = unsubscribe;

      // Subscribe to the specific pool
      await websocketService.subscribeToTradeData(poolAddr);
      
      setIsConnected(true);
      console.log(`✅ Successfully subscribed to trade data for pool: ${poolAddr}`);
    } catch (err: any) {
      console.error('❌ Failed to subscribe to trade data:', err);
      setError(err.message);
      setIsConnected(false);
    }
  }, [handleTradeDataUpdate]);

  // Unsubscribe from WebSocket trade data
  const unsubscribeFromTradeData = useCallback((poolAddr: string) => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    if (poolAddr) {
      websocketService.unsubscribeFromTradeData(poolAddr);
      console.log(`📡 Unsubscribed from trade data for pool: ${poolAddr}`);
    }

    setIsConnected(false);
  }, []);

  // Refetch data manually
  const refetch = useCallback(async () => {
    if (currentPoolAddress.current) {
      await fetchInitialTradeData(currentPoolAddress.current);
    }
  }, [fetchInitialTradeData]);

  // Main effect to handle pool address changes
  useEffect(() => {
    // Cleanup previous subscription
    if (currentPoolAddress.current) {
      unsubscribeFromTradeData(currentPoolAddress.current);
    }

    // Reset state
    setTrades([]);
    setError(null);
    setLastUpdate(null);
    setIsConnected(false);

    // Set new pool address
    currentPoolAddress.current = poolAddress;

    // Subscribe to new pool if provided
    if (poolAddress) {
      console.log(`🔄 Pool address changed to: ${poolAddress}`);
      
      // Fetch initial data first
      fetchInitialTradeData(poolAddress).then(() => {
        // Then subscribe to real-time updates
        subscribeToTradeData(poolAddress);
      });
    }

    // Cleanup on unmount or pool change
    return () => {
      if (currentPoolAddress.current) {
        unsubscribeFromTradeData(currentPoolAddress.current);
      }
    };
  }, [poolAddress, fetchInitialTradeData, subscribeToTradeData, unsubscribeFromTradeData]);

  return {
    trades,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    refetch
  };
};
