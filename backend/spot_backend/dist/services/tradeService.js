import axios from 'axios';
import { logger } from '../utils/logger.js';
import { getApiKey } from './coinService.js';
/**
 * Service for fetching trade data from Mobula API
 */
class TradeService {
    baseUrl = 'https://api.mobula.io/api/1';
    defaultLimit = 20;
    timeout = 25000;
    /**
     * Fetch initial trade data for a pool address
     */
    async fetchInitialTradeData(poolAddress, limit = this.defaultLimit) {
        try {
            logger.info(`Fetching initial trade data for pool: ${poolAddress}`);
            const apiKey = await getApiKey();
            if (!apiKey) {
                logger.error('❌ No API key available for Mobula trade data request');
                return [];
            }
            const response = await axios.get(`${this.baseUrl}/market/trades/pair`, {
                params: {
                    address: poolAddress,
                    blockchain: 'solana',
                    limit,
                    sortOrder: 'desc'
                },
                headers: {
                    Authorization: `Bearer ${apiKey}`,
                    accept: 'application/json'
                },
                timeout: this.timeout
            });
            logger.info(`✅ Successfully fetched ${response.data.data?.trades?.length || 0} initial trades for pool: ${poolAddress}`);
            return response.data.data?.trades || [];
        }
        catch (error) {
            logger.error(`❌ Error fetching initial trade data for pool ${poolAddress}:`, error.message || error);
            // Return empty array instead of throwing to allow graceful degradation
            return [];
        }
    }
    /**
     * Fetch trade data with pagination support
     */
    async fetchTradeDataWithPagination(poolAddress, options = {}) {
        try {
            const { limit = this.defaultLimit, page = 1, sortOrder = 'desc', fromTimestamp, toTimestamp } = options;
            logger.info(`Fetching paginated trade data for pool: ${poolAddress}, page: ${page}, limit: ${limit}`);
            const apiKey = await getApiKey();
            if (!apiKey) {
                logger.error('❌ No API key available for Mobula trade data request');
                return { trades: [] };
            }
            const params = {
                address: poolAddress,
                blockchain: 'solana',
                limit,
                sortOrder
            };
            // Add optional timestamp filters
            if (fromTimestamp) {
                params.from = fromTimestamp;
            }
            if (toTimestamp) {
                params.to = toTimestamp;
            }
            const response = await axios.get(`${this.baseUrl}/market/trades/pair`, {
                params,
                headers: {
                    Authorization: `Bearer ${apiKey}`,
                    accept: 'application/json'
                },
                timeout: this.timeout
            });
            const trades = response.data.data?.trades || [];
            const pagination = response.data.data?.pagination;
            logger.info(`✅ Successfully fetched ${trades.length} trades for pool: ${poolAddress}`);
            return {
                trades,
                pagination: pagination ? {
                    ...pagination,
                    hasMore: pagination.page * pagination.limit < pagination.total
                } : undefined
            };
        }
        catch (error) {
            logger.error(`❌ Error fetching paginated trade data for pool ${poolAddress}:`, error.message || error);
            return { trades: [] };
        }
    }
    /**
     * Validate pool address format
     */
    validatePoolAddress(poolAddress) {
        if (!poolAddress || typeof poolAddress !== 'string') {
            return false;
        }
        // Basic Solana address validation (base58, 32-44 characters)
        const solanaAddressRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
        return solanaAddressRegex.test(poolAddress);
    }
    /**
     * Format trade data for frontend consumption
     */
    formatTradeData(trades) {
        return trades.map(trade => ({
            id: trade.id,
            timestamp: trade.timestamp,
            type: trade.type,
            amount: trade.amount,
            price: trade.price,
            valueUsd: trade.value_usd,
            tokenAmount: trade.token_amount,
            tokenAmountUsd: trade.token_amount_usd,
            pair: trade.pair,
            blockchain: trade.blockchain,
            dex: trade.dex,
            wallet: trade.wallet,
            txHash: trade.tx_hash,
            // Add formatted display values
            displayAmount: this.formatNumber(trade.token_amount),
            displayPrice: this.formatPrice(trade.price),
            displayValueUsd: this.formatCurrency(trade.value_usd),
            timeAgo: this.getTimeAgo(trade.timestamp)
        }));
    }
    /**
     * Format number for display
     */
    formatNumber(num) {
        if (num >= 1e9) {
            return (num / 1e9).toFixed(2) + 'B';
        }
        else if (num >= 1e6) {
            return (num / 1e6).toFixed(2) + 'M';
        }
        else if (num >= 1e3) {
            return (num / 1e3).toFixed(2) + 'K';
        }
        else {
            return num.toFixed(2);
        }
    }
    /**
     * Format price for display
     */
    formatPrice(price) {
        if (price < 0.001) {
            return price.toExponential(3);
        }
        else if (price < 1) {
            return price.toFixed(6);
        }
        else {
            return price.toFixed(4);
        }
    }
    /**
     * Format currency for display
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }
    /**
     * Get time ago string
     */
    getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - (timestamp * 1000); // Convert to milliseconds
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0) {
            return `${days}d ago`;
        }
        else if (hours > 0) {
            return `${hours}h ago`;
        }
        else if (minutes > 0) {
            return `${minutes}m ago`;
        }
        else {
            return `${seconds}s ago`;
        }
    }
    /**
     * Get service health status
     */
    async getHealthStatus() {
        try {
            const apiKey = await getApiKey();
            if (!apiKey) {
                return {
                    status: 'unhealthy',
                    message: 'No API key available',
                    timestamp: Date.now()
                };
            }
            // Test with a simple request
            const testResponse = await axios.get(`${this.baseUrl}/market/trades/pair`, {
                params: {
                    address: '********************************************', // Test pool address
                    blockchain: 'solana',
                    limit: 1
                },
                headers: {
                    Authorization: `Bearer ${apiKey}`,
                    accept: 'application/json'
                },
                timeout: 5000
            });
            return {
                status: 'healthy',
                message: 'Trade service is operational',
                timestamp: Date.now()
            };
        }
        catch (error) {
            return {
                status: 'degraded',
                message: `Trade service error: ${error.message}`,
                timestamp: Date.now()
            };
        }
    }
}
// Export singleton instance
export const tradeService = new TradeService();
//# sourceMappingURL=tradeService.js.map