import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';

interface TradeData {
  pair: string;
  type: string;
  pairData: any;
  token_amount_usd: number;
  timestamp: number;
}

interface TradeSubscription {
  poolAddress: string;
  clientIds: Set<string>;
  tradeHistory: TradeData[];
  lastUpdate?: number;
}

/**
 * Service for managing Mobula Trade Feed WebSocket connections
 * Handles real-time trade data for specific pool addresses
 */
class MobulaTradeWebSocketService extends EventEmitter {
  private ws: WebSocket | null = null;
  private subscriptions = new Map<string, TradeSubscription>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 5000;
  private maxReconnectDelay = 300000; // 5 minutes
  private isConnecting = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private apiKey: string;
  private primaryWsUrl = 'wss://api.mobula.io';
  private fallbackWsUrl = 'wss://api-prod.mobula.io';
  private currentWsUrl: string;

  constructor() {
    super();
    this.apiKey = process.env.MOBULA_API_KEY || '';
    this.currentWsUrl = this.primaryWsUrl;
    
    if (!this.apiKey) {
      logger.error('❌ MOBULA_API_KEY not found in environment variables');
      throw new Error('MOBULA_API_KEY is required for trade feed service');
    }
    
    logger.info('🔧 Mobula Trade WebSocket Service initialized');
  }

  /**
   * Initialize the service
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('🚀 Initializing Mobula Trade WebSocket Service...');
      await this.connect();
      logger.info('✅ Mobula Trade WebSocket Service initialized successfully');
    } catch (error: any) {
      logger.error('❌ Failed to initialize Mobula Trade WebSocket Service:', error);
      throw error;
    }
  }

  /**
   * Connect to Mobula WebSocket
   */
  private async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      logger.debug('🔄 Already connecting or connected to Mobula Trade WebSocket');
      return;
    }

    this.isConnecting = true;
    logger.info(`🔌 Connecting to Mobula Trade WebSocket: ${this.currentWsUrl}`);

    try {
      this.ws = new WebSocket(this.currentWsUrl);
      this.setupWebSocketHandlers();

      // Wait for connection to open
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection timeout'));
        }, 10000);

        this.ws!.once('open', () => {
          clearTimeout(timeout);
          resolve();
        });

        this.ws!.once('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      
      logger.info('✅ Connected to Mobula Trade WebSocket successfully');
      
      // Resubscribe to existing subscriptions
      this.resubscribeAll();
    } catch (error: any) {
      this.isConnecting = false;
      logger.error('❌ Failed to connect to Mobula Trade WebSocket:', error);
      this.handleReconnect();
      throw error;
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.on('open', () => {
      logger.info('🔗 Mobula Trade WebSocket connection opened');
      this.emit('connected');
    });

    this.ws.on('message', (data: WebSocket.Data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleTradeMessage(message);
      } catch (error: any) {
        logger.error('❌ Failed to parse trade WebSocket message:', error);
      }
    });

    this.ws.on('close', (code: number, reason: Buffer) => {
      logger.warn(`🔌 Mobula Trade WebSocket closed: ${code} - ${reason.toString()}`);
      this.stopHeartbeat();
      this.emit('disconnected');
      
      if (code !== 1000) { // Not a normal closure
        this.handleReconnect();
      }
    });

    this.ws.on('error', (error: Error) => {
      logger.error('❌ Mobula Trade WebSocket error:', error);
      this.emit('error', error);
    });

    this.ws.on('pong', () => {
      logger.debug('🏓 Received pong from Mobula Trade WebSocket');
    });
  }

  /**
   * Handle incoming trade messages
   */
  private handleTradeMessage(message: any): void {
    try {
      // Handle different message types
      if (message.event && message.data) {
        // Error message format
        logger.warn('⚠️ Received error message from Mobula:', message);
        return;
      }

      // Check if this is trade data
      if (message.pair && message.type && message.pairData) {
        const tradeData: TradeData = message;
        const poolAddress = tradeData.pair;

        // Find subscription for this pool address
        const subscription = this.subscriptions.get(poolAddress);
        if (subscription) {
          // Add to trade history (keep last 100 trades)
          subscription.tradeHistory.unshift(tradeData);
          if (subscription.tradeHistory.length > 100) {
            subscription.tradeHistory = subscription.tradeHistory.slice(0, 100);
          }
          subscription.lastUpdate = Date.now();

          // Emit trade data to subscribers
          this.emit('tradeData', {
            poolAddress,
            tradeData,
            tradeHistory: subscription.tradeHistory
          });

          logger.debug(`📊 Received trade data for pool ${poolAddress}: ${tradeData.type} ${tradeData.token_amount_usd.toFixed(2)} USD`);
        }
      }
    } catch (error: any) {
      logger.error('❌ Error handling trade message:', error);
    }
  }

  /**
   * Subscribe to trade data for a specific pool address
   */
  public subscribeToPool(poolAddress: string, clientId: string): void {
    if (!poolAddress || !clientId) {
      logger.warn('⚠️ Invalid pool address or client ID for subscription');
      return;
    }

    let subscription = this.subscriptions.get(poolAddress);
    if (!subscription) {
      subscription = {
        poolAddress,
        clientIds: new Set(),
        tradeHistory: []
      };
      this.subscriptions.set(poolAddress, subscription);
    }

    subscription.clientIds.add(clientId);

    // Send subscription to Mobula if WebSocket is connected
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.sendSubscription(poolAddress);
    }

    logger.info(`📡 Client ${clientId} subscribed to pool ${poolAddress} (${subscription.clientIds.size} total clients)`);
  }

  /**
   * Unsubscribe from trade data for a specific pool address
   */
  public unsubscribeFromPool(poolAddress: string, clientId: string): void {
    const subscription = this.subscriptions.get(poolAddress);
    if (!subscription) return;

    subscription.clientIds.delete(clientId);

    if (subscription.clientIds.size === 0) {
      // No more clients for this pool, remove subscription
      this.subscriptions.delete(poolAddress);
      
      // Send unsubscription to Mobula if WebSocket is connected
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.sendUnsubscription(poolAddress);
      }
      
      logger.info(`📡 Removed subscription for pool ${poolAddress} (no more clients)`);
    } else {
      logger.info(`📡 Client ${clientId} unsubscribed from pool ${poolAddress} (${subscription.clientIds.size} remaining clients)`);
    }
  }

  /**
   * Send subscription message to Mobula
   */
  private sendSubscription(poolAddress: string): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      logger.warn('⚠️ Cannot send subscription - WebSocket not connected');
      return;
    }

    const subscriptionMessage = {
      type: 'pair',
      authorization: this.apiKey,
      payload: {
        blockchain: 'solana',
        address: poolAddress
      }
    };

    try {
      this.ws.send(JSON.stringify(subscriptionMessage));
      logger.info(`📤 Sent subscription for pool: ${poolAddress}`);
    } catch (error: any) {
      logger.error('❌ Failed to send subscription:', error);
    }
  }

  /**
   * Send unsubscription message to Mobula
   */
  private sendUnsubscription(poolAddress: string): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    const unsubscriptionMessage = {
      type: 'unsubscribe',
      payload: {}
    };

    try {
      this.ws.send(JSON.stringify(unsubscriptionMessage));
      logger.debug(`📤 Pool ${poolAddress} unsubscribed`);
    } catch (error: any) {
      logger.error('❌ Failed to send unsubscription:', error);
    }
  }

  /**
   * Resubscribe to all active subscriptions
   */
  private resubscribeAll(): void {
    for (const [poolAddress] of this.subscriptions) {
      this.sendSubscription(poolAddress);
    }
    logger.info(`🔄 Resubscribed to ${this.subscriptions.size} pools`);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.ping();
        logger.debug('🏓 Sent ping to Mobula Trade WebSocket');
      }
    }, 30000); // Every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('❌ Max reconnection attempts reached for Mobula Trade WebSocket');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
      this.maxReconnectDelay
    );

    logger.info(`🔄 Attempting to reconnect to Mobula Trade WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);

    setTimeout(() => {
      // Try fallback URL if primary failed
      if (this.reconnectAttempts > 2 && this.currentWsUrl === this.primaryWsUrl) {
        this.currentWsUrl = this.fallbackWsUrl;
        logger.info('🔄 Switching to fallback WebSocket URL');
      }

      this.connect().catch((error: any) => {
        logger.error('❌ Reconnection attempt failed:', error);
      });
    }, delay);
  }

  /**
   * Get trade history for a pool
   */
  public getTradeHistory(poolAddress: string): TradeData[] {
    const subscription = this.subscriptions.get(poolAddress);
    return subscription ? subscription.tradeHistory : [];
  }

  /**
   * Get service status
   */
  public getStatus(): {
    connected: boolean;
    subscriptions: number;
    reconnectAttempts: number;
    currentUrl: string;
  } {
    return {
      connected: this.ws?.readyState === WebSocket.OPEN,
      subscriptions: this.subscriptions.size,
      reconnectAttempts: this.reconnectAttempts,
      currentUrl: this.currentWsUrl
    };
  }

  /**
   * Shutdown the service
   */
  public shutdown(): void {
    logger.info('🛑 Shutting down Mobula Trade WebSocket Service...');

    this.stopHeartbeat();

    if (this.ws) {
      this.ws.close(1000, 'Service shutdown');
      this.ws = null;
    }

    this.subscriptions.clear();
    this.removeAllListeners();

    logger.info('✅ Mobula Trade WebSocket Service shutdown complete');
  }
}

// Export singleton instance
export const mobulaTradeWebSocketService = new MobulaTradeWebSocketService();
