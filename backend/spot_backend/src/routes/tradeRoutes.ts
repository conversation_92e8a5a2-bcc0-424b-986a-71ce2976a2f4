import express from 'express';
import {
  getInitialTradeData,
  getPaginatedTradeData,
  validatePoolAddress,
  getTradeServiceHealth,
  getTradeWebSocketStatus,
  getFrontendTradeStats
} from '../controllers/tradeController.js';
import { createApiRateLimiter } from '../middleware/rateLimiter.js';

const router = express.Router();

// Apply rate limiting to trade routes
const tradeRateLimit = createApiRateLimiter(100, 60000); // 100 requests per minute
router.use(tradeRateLimit.middleware());

// Health and status endpoints
router.get('/health', getTradeServiceHealth);
router.get('/websocket/status', getTradeWebSocketStatus);
router.get('/frontend/stats', getFrontendTradeStats);

// Pool address validation
router.get('/validate/:poolAddress', validatePoolAddress);

// Trade data endpoints
router.get('/initial/:poolAddress', getInitialTradeData);
router.get('/paginated/:poolAddress', getPaginatedTradeData);

export default router;
